'use server'
import { unstable_cache } from 'next/cache'

import { Component, Template } from '@/types/custom'
import { createClient } from '@/utils/supabase/client'

export const getTemplateCode = unstable_cache(
  async (id: string) => {
    const supabase = createClient()

    try {
      const { data, error } = await supabase
        .from('templates')
        .select('code')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching code:', error)
        return null
      }

      return data?.code
    } catch (error) {
      console.error('Unexpected error:', error)
      return null
    }
  },
  [`template-code`], // Include id in the cache key
  {
    tags: [`template-code`], // Add both general and specific tags
    revalidate: 86400, // Cache for 1 day
  }
)

export const getTemplate = async (id: string) => {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from('templates')
      .select('*')
      .eq('id', id)
      .single() // Assuming you want to fetch a single record

    // .maybeSingle()
    //   .range(0, 9)

    // console.log(data)
    return data
  } catch (error) {}
}

export const fetchCategoryTemplates = async (cat: string) => {
  try {
    const supabase = createClient()
    let query = supabase
      .from('templates')
      .select(
        `
          id, name, description, image_url, is_pro, meta_desc, tags, slug, likes,
          category:category_id!inner(name, slug)
        `
      )
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    // Adjust query based on the category
    if (cat === 'free') {
      query = query.eq('is_pro', false)
    } else if (cat === 'pro') {
      query = query.eq('is_pro', true)
    } else if (cat === 'popular') {
      // query = query.or(
      //   `tags.ilike.%${cat}%,description.ilike.%${cat}%,name.ilike.%${cat}%`
      // )
      query = query.gte('likes', 1000).order('likes', { ascending: false })
    } else if (cat === 'wireframes') {
      // For wireframes, search for 'wireframe' tag in the comma-separated tags string
      query = query.ilike('tags', '%wireframe%')
    } else {
      query = query.eq('category.slug', cat)
    }

    const { data: templates, error } = await query

    return { templates, error }
  } catch (error) {
    console.error('Error fetching components:', error)
    return { templates: null, error }
  }
}

export const fetchTemplateWithPagination = async (
  page: number,
  limit: number
) => {
  const supabase = createClient()
  const offset = (page - 1) * limit

  try {
    const {
      data: templates,
      count,
      error,
    } = await supabase
      .from('templates')
      .select('*', {
        count: 'exact',
      })
      .order('created_at', { ascending: false }) // Excluding code field
      .range(offset, offset + limit - 1)

    return { templates, count }
  } catch (error) {}
}

// fetching all templates used in templates page
export const fetchAllTemplates = unstable_cache(
  async () => {
    const supabase = createClient()
    try {
      const { data: templates, error } = (await supabase
        .from('templates')
        .select(
          ` id, name, description, image_url,
        tags, meta_desc, is_pro, slug, likes,
        category:category_id(name, slug)
        `
        )
        .eq('is_active', true)
        .order('created_at', { ascending: false })) as {
        data: Component[] | null
        error: any
      }

      return { templates, error }
    } catch (error) {}
  },
  ['all-templates'], // add the
  {
    tags: ['templates'],
    revalidate: 86400, // in seconds its equal to 24 hours
  }
)

export const searchTemplates = async (query: string) => {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from('templates')
      .select(
        'id, name, description, image_url, tags,meta_desc , is_pro , slug'
      )
      .or(
        `name.ilike.%${query}%,tags.ilike.%${query}%,description.ilike.%${query}%`
      )

    if (error) {
      console.error('Error searching templates:', error)
      return []
    }

    // console.log(data)

    return data
  } catch (error) {
    console.error('Unexpected error:', error)
    return []
  }
}

export const fetchTemplateBySlug = async (slug: string) => {
  const supabase = createClient()
  try {
    const { data: component, error } = (await supabase
      .from('templates')
      .select(
        `id, name, description, image_url, category_id, is_pro, meta_desc, tags, slug, likes`
      )
      .eq('slug', slug)
      .single()) as {
      data: Template | null
      error: any
    }

    return { component, error }
  } catch (error) {
    console.error('Error fetching component:', error)
    return { component: null, error }
  }
}

export const fetchTemplateMetadata = async (slug: string) => {
  const supabase = createClient()
  try {
    const { data: template, error } = await supabase
      .from('templates')
      .select('name, description, meta_desc, image_url')
      .eq('slug', slug)
      .single()

    return { component: template, error }
  } catch (error) {
    console.error('Error fetching template metadata:', error)
    return { component: null, error }
  }
}

export async function fetchTemplatesForPaths() {
  const supabase = createClient()

  try {
    const { data: templates, error } = await supabase
      .from('templates')
      .select(
        `
        slug,
        category:category_id!inner(slug)
      `
      )
      .eq('is_active', true)
      .not('slug', 'is', null)

    if (error) {
      throw new Error('Failed to fetch templates')
    }

    return (templates || [])
      .map((template: any) => ({
        category_slug: template.category?.slug,
        slug: template.slug,
      }))
      .filter((item) => item.category_slug && item.slug) // Filter out any null values
  } catch (error) {
    console.error('Error fetching templates for paths:', error)
    return [] // Return an empty array to avoid build failures
  }
}
