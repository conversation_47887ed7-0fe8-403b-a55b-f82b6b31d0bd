'use client'
import { useMutation } from '@tanstack/react-query'
import { AnimatePresence, motion } from 'framer-motion'
import { CheckCircle, Loader2, Sparkles, X } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'

import { ConfettiEffect } from '@/components/custom/confetti-effect'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useProfileStore } from '@/stores/profile-store'
import { createClient } from '@/utils/supabase/client'

interface CouponCheckoutDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface CouponValidationResponse {
  valid: boolean
  coupon?: {
    id: string
    code: string
    description: string
    discount_percentage: number
    discount_amount: number
  }
  error?: string
}

interface CouponCheckoutResponse {
  success: boolean
  transaction_id?: string
  message?: string
  error?: string
}

// Constants
const ORIGINAL_PRICE = 299

export function CouponCheckoutDialog({
  open,
  onOpenChange,
}: CouponCheckoutDialogProps) {
  const [couponCode, setCouponCode] = useState('')
  const [validatedCoupon, setValidatedCoupon] =
    useState<CouponValidationResponse | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const { user, fetchUser } = useProfileStore()
  const supabase = createClient()

  // Reset state when dialog closes
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setCouponCode('')
      setValidatedCoupon(null)
      setIsValidating(false)
      setShowSuccess(false)
    }
    onOpenChange(newOpen)
  }

  // Validate coupon code
  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error('Please enter a coupon code')
      return
    }
    setIsValidating(true)
    try {
      const { data: coupon, error } = await supabase
        .from('coupons')
        .select(
          'id, code, description, discount_percentage, discount_amount, limit, valid_from, valid_to'
        )
        .eq('code', couponCode.toUpperCase())
        .gte('valid_to', new Date().toISOString())
        .lte('valid_from', new Date().toISOString())
        .single()
      if (error || !coupon) {
        setValidatedCoupon({
          valid: false,
          error: 'Invalid or expired coupon code',
        })
        toast.error('Invalid or expired coupon code')
        return
      }
      if (coupon.limit !== null && coupon.limit <= 0) {
        setValidatedCoupon({ valid: false, error: 'Coupon limit exceeded' })
        toast.error('Coupon limit exceeded')
        return
      }
      // Check if it's a 100% off coupon (lifetime deal requirement)
      const isLifetimeDeal = coupon.discount_percentage === 100
      if (!isLifetimeDeal) {
        setValidatedCoupon({
          valid: false,
          error: 'This coupon is not valid for lifetime deals',
        })
        toast.error('This coupon is not valid for lifetime deals')
        return
      }
      setValidatedCoupon({ valid: true, coupon })
      toast.success('Coupon validated successfully!')
    } catch (error) {
      console.error('Coupon validation error:', error)
      setValidatedCoupon({ valid: false, error: 'Failed to validate coupon' })
      toast.error('Failed to validate coupon')
    } finally {
      setIsValidating(false)
    }
  }

  // Process coupon checkout
  const checkoutMutation = useMutation({
    mutationFn: async () => {
      if (!validatedCoupon?.valid || !validatedCoupon.coupon || !user) {
        throw new Error('Invalid state for checkout')
      }
      const response = await fetch('/api/coupon-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          coupon_code: validatedCoupon.coupon.code,
          coupon_id: validatedCoupon.coupon.id,
          subscription_plan_id: 'dfcdbb27-1bfc-4739-97bf-e7fb7df65ee3',
          user_id: user.id,
        }),
      })
      const data: CouponCheckoutResponse = await response.json()
      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Checkout failed')
      }
      return data
    },
    onSuccess: async () => {
      // Show success state with confetti
      setShowSuccess(true)
      // Refresh user profile to reflect the changes
      await fetchUser()
      // Show success toast
      toast.success('🎉 Congratulations! You now have lifetime pro access!')
      // Close dialog after a delay to show success animation
      setTimeout(() => {
        handleOpenChange(false)
      }, 3000)
    },
    onError: (error) => {
      console.error('Checkout error:', error)
      toast.error(error.message || 'Checkout failed. Please try again.')
    },
  })

  const handleCheckout = () => {
    if (!user) {
      toast.error('Please log in to continue')
      return
    }
    if (!validatedCoupon?.valid) {
      toast.error('Please validate your coupon first')
      return
    }
    checkoutMutation.mutate()
  }

  const finalPrice = validatedCoupon?.valid ? 0 : ORIGINAL_PRICE

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent
        className="overflow-hidden sm:max-w-lg



      "
      >
        {/* Success Confetti */}
        {showSuccess && <ConfettiEffect duration={3000} numberOfPieces={150} />}
        <AnimatePresence mode="wait">
          {showSuccess ? (
            // Success State
            <motion.div
              key="success"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.5, type: 'spring' }}
              className="py-8 text-center"
            >
              <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg">
                <CheckCircle className="h-10 w-10 text-white" />
              </div>
              <h3 className="mb-2 text-2xl font-bold text-foreground">
                🎉 Welcome to Pro!
              </h3>
              <p className="text-muted-foreground">
                Your lifetime pro access has been activated successfully!
              </p>
            </motion.div>
          ) : (
            // Main Checkout Form
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <DialogHeader className="text-center">
                <DialogTitle className="text-2xl font-bold">
                  Lifetime Deal Checkout
                </DialogTitle>
                <DialogDescription className="text-base">
                  Enter your 100% off coupon code to activate your lifetime pro
                  access.
                </DialogDescription>
              </DialogHeader>
              <div className="mt-6 space-y-6">
                {/* Coupon Input Section */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="space-y-3"
                >
                  <Label htmlFor="coupon-code" className="text-sm font-medium">
                    Coupon Code
                  </Label>
                  <div className="flex gap-0">
                    {' '}
                    {/* Remove gap between input and button */}
                    <div className="relative flex flex-1">
                      {' '}
                      {/* Container for input and button */}
                      <Input
                        id="coupon-code"
                        value={couponCode}
                        onChange={(e) =>
                          setCouponCode(e.target.value.toUpperCase())
                        }
                        placeholder="Enter your coupon code"
                        disabled={isValidating || checkoutMutation.isPending}
                        className="h-11 flex-1  text-start font-mono text-lg tracking-normal focus:border-transparent focus:outline-none focus:ring-2 focus:ring-ring" // Remove right border radius and adjust focus styles
                      />
                      <Button
                        onClick={validateCoupon}
                        disabled={
                          !couponCode.trim() ||
                          isValidating ||
                          checkoutMutation.isPending
                        }
                        variant="default"
                        size="lg"
                        className="absolute right-0 h-11 rounded-l-none border-l-0 px-6" // Remove left border radius and left border
                      >
                        {isValidating ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          'Apply Coupon'
                        )}
                      </Button>
                    </div>
                  </div>
                </motion.div>

                {/* Validation Feedback */}
                <AnimatePresence>
                  {validatedCoupon && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95, y: -10 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.95, y: -10 }}
                      transition={{ duration: 0.2 }}
                      className={`flex items-center gap-3 rounded-lg border p-4 ${
                        validatedCoupon.valid
                          ? 'border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-300'
                          : 'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300'
                      }`}
                    >
                      {validatedCoupon.valid ? (
                        <>
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                            <CheckCircle className="h-4 w-4" />
                          </div>
                          <div>
                            <p className="font-medium">Coupon validated!</p>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100 dark:bg-red-900">
                            <X className="h-4 w-4" />
                          </div>
                          <div>
                            <p className="font-medium">Invalid coupon</p>
                            <p className="text-sm opacity-80">
                              {validatedCoupon.error}
                            </p>
                          </div>
                        </>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Price Display */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="space-y-4 rounded-xl border border-primary/20

                  bg-gradient-to-r from-primary/20 to-primary/10 p-6 shadow-sm

                  "
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Sparkles className="h-4 w-4 text-primary" />
                      <span className="font-medium">Lifetime Pro Access</span>
                    </div>
                    <span
                      className={`font-mono text-lg ${
                        finalPrice === 0
                          ? 'text-muted-foreground line-through'
                          : 'text-foreground'
                      }`}
                    >
                      ${ORIGINAL_PRICE}
                    </span>
                  </div>
                  <AnimatePresence>
                    {validatedCoupon?.valid && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="flex items-center justify-between text-green-600 dark:text-green-400"
                      >
                        <span className="text-sm font-medium">
                          🎉 Coupon Discount (100% off)
                        </span>
                        <span className="font-mono text-sm">
                          -${ORIGINAL_PRICE}
                        </span>
                      </motion.div>
                    )}
                  </AnimatePresence>
                  <div className="flex items-center justify-between border-t border-border pt-4">
                    <span className="text-lg font-semibold">Total</span>
                    <div className="flex items-center gap-3">
                      <span className="font-mono text-2xl font-bold">
                        ${finalPrice}
                      </span>
                      {finalPrice === 0 && (
                        <Badge variant="success" className="animate-pulse">
                          FREE
                        </Badge>
                      )}
                    </div>
                  </div>
                </motion.div>

                {/* Checkout Button */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Button
                    onClick={handleCheckout}
                    disabled={
                      !validatedCoupon?.valid || checkoutMutation.isPending
                    }
                    variant="gradient-primary"
                    size="lg"
                    className="h-12 w-full text-base font-semibold"
                  >
                    {checkoutMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Processing your request...
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-5 w-5" />
                        Activate Lifetime Pro Access
                      </>
                    )}
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  )
}
