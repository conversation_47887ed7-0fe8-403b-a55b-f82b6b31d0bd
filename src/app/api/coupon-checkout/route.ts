import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

import { createClient } from '@/utils/supabase/server'

function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

  if (!serviceRoleKey) {
    throw new Error(
      'SUPABASE_SERVICE_ROLE_KEY is required for webhook operations'
    )
  }

  return createSupabaseClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  })
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    console.log('Starting coupon checkout process...')

    const supabase = createClient()

    const supabaseAdmin = createServiceRoleClient()

    // Get authenticated user
    const { data: userResult, error: userError } = await supabase.auth.getUser()

    if (userError || !userResult?.user) {
      console.error('User authentication error:', userError)
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      )
    }

    const user = userResult.user
    const { coupon_code, coupon_id, subscription_plan_id, user_id } =
      await request.json()

    // Validate required fields
    if (!coupon_code || !coupon_id || !subscription_plan_id || !user_id) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify user owns this request
    if (user.id !== user_id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized access' },
        { status: 403 }
      )
    }

    // Validate subscription plan exists
    const { data: subscriptionPlan, error: planError } = await supabaseAdmin
      .from('subscription_plans')
      .select('*')
      .eq('id', subscription_plan_id)
      .single()

    if (planError || !subscriptionPlan) {
      console.error('Subscription plan error:', planError)
      return NextResponse.json(
        { success: false, error: 'Invalid subscription plan' },
        { status: 400 }
      )
    }

    // Validate and fetch coupon
    const { data: coupon, error: couponError } = await supabaseAdmin
      .from('coupons')
      .select('*')
      .eq('id', coupon_id)
      .eq('code', coupon_code.toUpperCase())
      .gte('valid_to', new Date().toISOString())
      .lte('valid_from', new Date().toISOString())
      .single()

    if (couponError || !coupon) {
      console.error('Coupon validation error:', couponError)
      return NextResponse.json(
        { success: false, error: 'Invalid or expired coupon' },
        { status: 400 }
      )
    }

    // Check coupon limit
    if (coupon.limit !== null && coupon.limit <= 0) {
      return NextResponse.json(
        { success: false, error: 'Coupon limit exceeded' },
        { status: 400 }
      )
    }

    // Verify it's a 100% off coupon (lifetime deal requirement)
    if (coupon.discount_percentage !== 100) {
      return NextResponse.json(
        {
          success: false,
          error: 'This coupon is not valid for lifetime deals',
        },
        { status: 400 }
      )
    }

    // Check if user already has lifetime access
    const { data: existingProfile } = await supabaseAdmin
      .from('profiles')
      .select('is_lifetime_pro, is_pro')
      .eq('id', user_id)
      .single()

    // if (existingProfile?.is_lifetime_pro) {
    //   return NextResponse.json(
    //     { success: false, error: 'You already have lifetime pro access' },
    //     { status: 400 }
    //   )
    // }

    // Create transaction record
    const transactionData = {
      user_id: user_id,
      subscription_id: subscription_plan_id,
      amount: 0, // 100% off
      currency: 'USD',
      status: 'completed',
      payment_method: 'coupon',
      coupon_id: coupon.id,
      metadata: {
        coupon_code: coupon.code,
        original_amount: subscriptionPlan.unit_amount,
        discount_percentage: coupon.discount_percentage,
        checkout_type: 'lifetime_deal',
      },
    }

    const { data: transaction, error: transactionError } = await supabaseAdmin
      .from('transactions')
      .insert(transactionData)
      .select()
      .single()

    if (transactionError) {
      console.error('Transaction creation error:', transactionError)
      return NextResponse.json(
        { success: false, error: 'Failed to create transaction' },
        { status: 500 }
      )
    }

    // Update user profile to lifetime pro
    const { error: profileUpdateError } = await supabase
      .from('profiles')
      .update({
        is_pro: true,
        is_lifetime_pro: true,
        is_lifetime_subscription: true,
        current_subscription_plan_id: subscription_plan_id,
        subscription_status: 'active',
        subscription_start_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', user_id)

    if (profileUpdateError) {
      console.error('Profile update error:', profileUpdateError)
      // Note: Transaction was created but profile update failed
      // In a production system, you might want to implement compensation logic
      return NextResponse.json(
        { success: false, error: 'Failed to update user profile' },
        { status: 500 }
      )
    }

    // Decrease coupon limit if applicable
    if (coupon.limit !== null) {
      const { error: couponUpdateError } = await supabase
        .from('coupons')
        .update({ limit: coupon.limit - 1 })
        .eq('id', coupon.id)

      if (couponUpdateError) {
        console.error('Coupon limit update error:', couponUpdateError)
        // This is not critical, so we don't fail the entire operation
      }
    }

    console.log('Coupon checkout completed successfully:', {
      transaction_id: transaction.id,
      user_id: user_id,
      coupon_code: coupon.code,
    })

    return NextResponse.json({
      success: true,
      transaction_id: transaction.id,
      message: 'Lifetime pro access activated successfully!',
    })
  } catch (error) {
    console.error('Coupon checkout error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
